<?php

namespace Tests\Unit;

use App\Services\WebhookEventRegistry;
use PHPUnit\Framework\TestCase;

class WebhookEventRegistryTest extends TestCase
{
    private WebhookEventRegistry $registry;

    protected function setUp(): void
    {
        parent::setUp();
        $this->registry = new WebhookEventRegistry();
    }

    public function test_can_register_single_event()
    {
        $this->registry->register('test.event', 'Test Event', 'test');
        
        $this->assertTrue($this->registry->isRegistered('test.event'));
        $this->assertContains('test.event', $this->registry->getAllEventTypes());
        
        $event = $this->registry->getEvent('test.event');
        $this->assertEquals('Test Event', $event['description']);
        $this->assertEquals('test', $event['group']);
    }

    public function test_can_register_multiple_events()
    {
        $events = [
            'user.created' => 'User Created',
            'user.updated' => 'User Updated',
            'user.deleted' => 'User Deleted',
        ];

        $this->registry->registerMany($events, 'user');

        foreach (array_keys($events) as $eventType) {
            $this->assertTrue($this->registry->isRegistered($eventType));
        }

        $userEvents = $this->registry->getEventsInGroup('user');
        $this->assertCount(3, $userEvents);
    }

    public function test_k8s_events_registration()
    {
        $this->registry->registerK8sEvents();

        // Test some key K8s events
        $this->assertTrue($this->registry->isRegistered('deployment.created'));
        $this->assertTrue($this->registry->isRegistered('service.updated'));
        $this->assertTrue($this->registry->isRegistered('pod.deleted'));
        
        // Test that scaled events are not registered for certain resources
        $this->assertFalse($this->registry->isRegistered('secret.scaled'));
        $this->assertFalse($this->registry->isRegistered('event.scaled'));
    }

    public function test_validation_event_types_includes_wildcard()
    {
        $this->registry->register('test.event', 'Test Event');
        
        $validationTypes = $this->registry->getValidationEventTypes();
        
        $this->assertContains('*', $validationTypes);
        $this->assertContains('test.event', $validationTypes);
    }

    public function test_events_grouped_correctly()
    {
        $this->registry->register('user.created', 'User Created', 'user');
        $this->registry->register('system.alert', 'System Alert', 'system');
        
        $eventsByGroup = $this->registry->getEventsByGroup();
        
        $this->assertArrayHasKey('user', $eventsByGroup);
        $this->assertArrayHasKey('system', $eventsByGroup);
        
        $this->assertCount(1, $eventsByGroup['user']);
        $this->assertCount(1, $eventsByGroup['system']);
    }

    public function test_can_clear_registry()
    {
        $this->registry->register('test.event', 'Test Event');
        $this->assertTrue($this->registry->isRegistered('test.event'));
        
        $this->registry->clear();
        $this->assertFalse($this->registry->isRegistered('test.event'));
        $this->assertEmpty($this->registry->getAllEventTypes());
    }
}
