<?php

namespace Tests\Feature;

use App\Events\K8s\BaseK8sResourceEvent;
use App\Models\User;
use App\Models\Workspace;
use App\Models\WebhookEndpoint;
use App\Notifications\K8sResourceWebhookNotification;
use App\Notifications\GenericWebhookNotification;
use App\Services\WebhookEventRegistry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class WebhookNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 禁用事件监听器，避免干扰测试
        Event::fake();
        Notification::fake();
    }

    /** @test */
    public function webhook_event_registry_can_register_events()
    {
        $registry = app(WebhookEventRegistry::class);
        
        // 测试注册单个事件
        $registry->register('test.event', 'Test Event', 'test');
        
        $this->assertTrue($registry->isRegistered('test.event'));
        $this->assertContains('test.event', $registry->getAllEventTypes());
        
        // 测试获取事件信息
        $event = $registry->getEvent('test.event');
        $this->assertEquals('Test Event', $event['description']);
        $this->assertEquals('test', $event['group']);
    }

    /** @test */
    public function webhook_event_registry_includes_k8s_events()
    {
        $registry = app(WebhookEventRegistry::class);
        
        // 检查 K8s 事件是否已注册
        $this->assertTrue($registry->isRegistered('deployment.created'));
        $this->assertTrue($registry->isRegistered('service.updated'));
        $this->assertTrue($registry->isRegistered('pod.deleted'));
        
        // 检查验证事件类型包含通配符
        $validationTypes = $registry->getValidationEventTypes();
        $this->assertContains('*', $validationTypes);
        $this->assertContains('deployment.created', $validationTypes);
    }

    /** @test */
    public function workspace_can_send_webhook_notifications()
    {
        $user = User::factory()->create();
        $workspace = Workspace::factory()->create(['user_id' => $user->id]);
        
        // 创建 webhook 端点
        $endpoint = WebhookEndpoint::factory()->create([
            'workspace_id' => $workspace->id,
            'events' => ['test'],
            'is_active' => true,
        ]);

        // 发送通知
        $workspace->notifyWebhook('test', ['message' => 'Test notification']);

        // 验证通知已发送
        Notification::assertSentTo($workspace, GenericWebhookNotification::class);
    }

    /** @test */
    public function k8s_resource_notification_can_be_created_from_event()
    {
        // 创建模拟的 K8s 事件
        $event = new class('test-namespace', 'test-cluster', 1, 'deployment', 'test-deployment', [], 'created') extends BaseK8sResourceEvent {};

        // 从事件创建通知
        $notification = K8sResourceWebhookNotification::fromK8sEvent($event);

        $this->assertEquals('deployment.created', $notification->getEventType());
        $this->assertEquals('test-namespace', $notification->getEventData()['namespace']);
        $this->assertEquals('test-deployment', $notification->getEventData()['resource_name']);
    }

    /** @test */
    public function webhook_notification_builds_correct_payload()
    {
        $workspace = Workspace::factory()->create();
        
        $notification = new GenericWebhookNotification('test.event', [
            'message' => 'Test message',
            'data' => ['key' => 'value'],
        ]);

        $webhookData = $notification->toWebhook($workspace);

        $this->assertEquals('test.event', $webhookData['event_type']);
        $this->assertArrayHasKey('event_id', $webhookData['payload']);
        $this->assertArrayHasKey('timestamp', $webhookData['payload']);
        $this->assertEquals('test.event', $webhookData['payload']['event_type']);
        $this->assertEquals('Test message', $webhookData['payload']['data']['message']);
    }

    /** @test */
    public function webhook_endpoint_should_receive_event_works_correctly()
    {
        $endpoint = WebhookEndpoint::factory()->create([
            'events' => ['deployment.created', 'service.updated'],
            'is_active' => true,
        ]);

        $this->assertTrue($endpoint->shouldReceiveEvent('deployment.created'));
        $this->assertTrue($endpoint->shouldReceiveEvent('service.updated'));
        $this->assertFalse($endpoint->shouldReceiveEvent('pod.deleted'));

        // 测试通配符
        $wildcardEndpoint = WebhookEndpoint::factory()->create([
            'events' => ['*'],
            'is_active' => true,
        ]);

        $this->assertTrue($wildcardEndpoint->shouldReceiveEvent('any.event'));
        $this->assertTrue($wildcardEndpoint->shouldReceiveEvent('deployment.created'));

        // 测试非活跃端点
        $inactiveEndpoint = WebhookEndpoint::factory()->create([
            'events' => ['*'],
            'is_active' => false,
        ]);

        $this->assertFalse($inactiveEndpoint->shouldReceiveEvent('any.event'));
    }

    /** @test */
    public function webhook_requests_use_event_registry_for_validation()
    {
        $user = User::factory()->create();
        $workspace = Workspace::factory()->create(['user_id' => $user->id]);

        // 测试有效事件
        $response = $this->actingAs($user)->postJson('/api/webhooks', [
            'name' => 'Test Webhook',
            'url' => 'https://example.com/webhook',
            'events' => ['deployment.created', 'test'],
            'workspace_id' => $workspace->id,
        ]);

        $response->assertStatus(201);

        // 测试无效事件
        $response = $this->actingAs($user)->postJson('/api/webhooks', [
            'name' => 'Test Webhook',
            'url' => 'https://example.com/webhook',
            'events' => ['invalid.event'],
            'workspace_id' => $workspace->id,
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['events.0']);
    }
}
