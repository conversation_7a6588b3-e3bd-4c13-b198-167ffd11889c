<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Webhook Events Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for webhook events that can be
    | registered in addition to the default events. You can organize events
    | by groups for better management.
    |
    */

    'events' => [
        // 自定义系统事件
        'custom' => [
            'billing.invoice_created' => '账单创建',
            'billing.payment_received' => '付款接收',
            'billing.subscription_expired' => '订阅过期',
        ],

        // 集成相关事件
        'integrations' => [
            'github.push' => 'GitHub 推送事件',
            'gitlab.push' => 'GitLab 推送事件',
            'docker.image_pushed' => 'Docker 镜像推送',
        ],

        // 监控和告警事件
        'monitoring' => [
            'alert.cpu_high' => 'CPU 使用率过高',
            'alert.memory_high' => '内存使用率过高',
            'alert.disk_full' => '磁盘空间不足',
            'alert.service_down' => '服务宕机',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Notification Settings
    |--------------------------------------------------------------------------
    |
    | These settings will be used as defaults when sending webhook notifications.
    |
    */

    'defaults' => [
        'timeout' => 30,
        'max_attempts' => 3,
        'verify_ssl' => true,
        'headers' => [
            'Content-Type' => 'application/json',
            'User-Agent' => 'PaaS-Webhook/1.0',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Payload Configuration
    |--------------------------------------------------------------------------
    |
    | Configure how webhook payloads are structured.
    |
    */

    'payload' => [
        // 是否在负载中包含事件 ID
        'include_event_id' => true,
        
        // 是否在负载中包含时间戳
        'include_timestamp' => true,
        
        // 时间戳格式
        'timestamp_format' => 'c', // ISO 8601 格式
        
        // 是否在负载中包含发送者信息
        'include_sender' => true,
        
        // 发送者信息
        'sender' => [
            'name' => 'PaaS Platform',
            'version' => '1.0',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Security-related configuration for webhooks.
    |
    */

    'security' => [
        // 默认签名算法
        'signature_algorithm' => 'sha256',
        
        // 签名头名称
        'signature_header' => 'X-Webhook-Signature',
        
        // 是否自动生成密钥
        'auto_generate_secret' => true,
        
        // 密钥长度
        'secret_length' => 40,
    ],
];
