<script setup lang="ts">
import { <PERSON>, Link, router } from '@inertiajs/vue3';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import AppLayout from '@/layouts/AppLayout.vue';
import { 
  ArrowLeft, 
  Settings, 
  TestTube, 
  ToggleLeft, 
  ToggleRight, 
  Key,
  Copy,
  CheckCircle,
  XCircle,
  Clock,
  MoreHorizontal,
  Eye,
  BarChart3
} from 'lucide-vue-next';
import { ref } from 'vue';
import type { WebhookEndpoint, WebhookDelivery, WebhookStats } from '@/types/webhook';
import type { BreadcrumbItemType } from '@/types';

interface Props {
  webhook: WebhookEndpoint;
  deliveries: WebhookDelivery[];
  stats: WebhookStats;
}

const props = defineProps<Props>();

const breadcrumbs: BreadcrumbItemType[] = [
  {
    title: 'Webhook 管理',
    href: '/webhooks',
  },
  {
    title: props.webhook.name,
    href: `/webhooks/${props.webhook.id}`,
  },
];

const showSecret = ref(false);

const formatDate = (date: string | null) => {
  if (!date) return '从未';
  return new Date(date).toLocaleString('zh-CN');
};

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'success':
      return { variant: 'default' as const, text: '成功', icon: CheckCircle };
    case 'failed':
      return { variant: 'destructive' as const, text: '失败', icon: XCircle };
    case 'pending':
      return { variant: 'secondary' as const, text: '待处理', icon: Clock };
    default:
      return { variant: 'secondary' as const, text: status, icon: Clock };
  }
};

const copyToClipboard = async (text: string, label: string) => {
  try {
    await navigator.clipboard.writeText(text);
    toast.success('已复制', {
      description: `${label}已复制到剪贴板`
    });
  } catch (error) {
    toast.error('复制失败', {
      description: '无法访问剪贴板'
    });
  }
};

const testWebhook = async () => {
  try {
    await router.post(`/webhooks/${props.webhook.id}/test`, {}, {
      preserveState: true,
      preserveScroll: true,
      onSuccess: (page) => {
        const result = page.props.flash?.test_result;
        if (result?.success) {
          toast.success('测试成功', {
            description: result.message || '已发送测试Webhook'
          });
        } else {
          toast.error('测试失败', {
            description: result?.error_message || '未知错误'
          });
        }
      },
      onError: (errors) => {
        toast.error('测试失败', {
          description: Object.values(errors)[0] as string
        });
      }
    });
  } catch (error) {
    toast.error('测试失败', {
      description: '请求发送失败'
    });
  }
};

const toggleWebhook = async () => {
  try {
    await router.post(`/webhooks/${props.webhook.id}/toggle`, {}, {
      preserveState: true,
      preserveScroll: true,
      onSuccess: () => {
        const action = props.webhook.is_active ? '禁用' : '启用';
        toast.success(`${action}成功`, {
          description: `已${action} ${props.webhook.name}`
        });
      },
      onError: (errors) => {
        toast.error('操作失败', {
          description: Object.values(errors)[0] as string
        });
      }
    });
  } catch (error) {
    toast.error('操作失败', {
      description: '请求发送失败'
    });
  }
};

const regenerateSecret = async () => {
  try {
    await router.post(`/webhooks/${props.webhook.id}/regenerate-secret`, {}, {
      preserveState: true,
      preserveScroll: true,
      onSuccess: () => {
        toast.success('密钥重新生成成功', {
          description: `已为 ${props.webhook.name} 生成新的签名密钥`
        });
      },
      onError: (errors) => {
        toast.error('操作失败', {
          description: Object.values(errors)[0] as string
        });
      }
    });
  } catch (error) {
    toast.error('操作失败', {
      description: '请求发送失败'
    });
  }
};
</script>

<template>
  <Head :title="webhook.name" />

  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="p-4 space-y-6">
      <!-- 头部 -->
      <div class="flex items-center justify-between">
        <div>
          <div class="flex items-center gap-3">
            <h1 class="text-3xl font-bold">{{ webhook.name }}</h1>
            <Badge
              :variant="webhook.is_active ? 'default' : 'secondary'"
            >
              {{ webhook.is_active ? '活跃' : '已禁用' }}
            </Badge>
          </div>
          <p class="text-muted-foreground mt-2">
            {{ webhook.url }}
          </p>
        </div>
      </div>
      
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="testWebhook">
          <TestTube class="mr-2 h-4 w-4" />
          发送测试
        </Button>
        
        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="outline" size="sm">
              <MoreHorizontal class="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem as-child>
              <Link :href="route('webhooks.edit', webhook.id)">
                <Settings class="mr-2 h-4 w-4" />
                编辑设置
              </Link>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem @click="toggleWebhook">
              <component
                :is="webhook.is_active ? ToggleLeft : ToggleRight"
                class="mr-2 h-4 w-4"
              />
              {{ webhook.is_active ? '禁用' : '启用' }}
            </DropdownMenuItem>
            
            <DropdownMenuItem @click="regenerateSecret">
              <Key class="mr-2 h-4 w-4" />
              重新生成密钥
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 主内容区域 -->
      <div class="lg:col-span-2">
        <Tabs default-value="deliveries" class="space-y-6">
          <TabsList>
            <TabsTrigger value="deliveries">发送记录</TabsTrigger>
            <TabsTrigger value="config">配置详情</TabsTrigger>
          </TabsList>

          <!-- 发送记录 -->
          <TabsContent value="deliveries" class="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <Eye class="h-5 w-5" />
                  发送记录
                </CardTitle>
                <CardDescription>
                  最近的 Webhook 发送记录和状态
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div class="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>事件类型</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>HTTP状态码</TableHead>
                        <TableHead>耗时</TableHead>
                        <TableHead>发送时间</TableHead>
                        <TableHead class="w-12"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow v-if="props.deliveries.length === 0">
                        <TableCell colspan="6" class="text-center py-8 text-muted-foreground">
                          暂无发送记录
                        </TableCell>
                      </TableRow>

                      <TableRow v-for="delivery in props.deliveries" :key="delivery.id">
                        <TableCell class="font-medium">
                          <Badge variant="outline">
                            {{ delivery.event_type }}
                          </Badge>
                        </TableCell>
                        
                        <TableCell>
                          <div class="flex items-center gap-2">
                            <component 
                              :is="getStatusBadge(delivery.status).icon"
                              class="h-4 w-4"
                              :class="{
                                'text-green-600': delivery.status === 'success',
                                'text-red-600': delivery.status === 'failed',
                                'text-yellow-600': delivery.status === 'pending'
                              }"
                            />
                            <span class="text-sm">
                              {{ getStatusBadge(delivery.status).text }}
                            </span>
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <span v-if="delivery.http_status_code" class="font-mono text-sm">
                            {{ delivery.http_status_code }}
                          </span>
                          <span v-else class="text-muted-foreground">-</span>
                        </TableCell>
                        
                        <TableCell>
                          <span v-if="delivery.formatted_duration" class="text-sm">
                            {{ delivery.formatted_duration }}
                          </span>
                          <span v-else class="text-muted-foreground">-</span>
                        </TableCell>
                        
                        <TableCell class="text-sm text-muted-foreground">
                          {{ formatDate(delivery.delivered_at || delivery.created_at) }}
                        </TableCell>
                        
                        <TableCell>
                          <Button variant="ghost" size="sm" as-child>
                            <Link :href="route('webhooks.deliveries.show', { webhook: webhook.id, delivery: delivery.id })">
                              <Eye class="h-4 w-4" />
                            </Link>
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 配置详情 -->
          <TabsContent value="config" class="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>基础配置</CardTitle>
              </CardHeader>
              <CardContent class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <Label class="text-sm font-medium">名称</Label>
                    <p class="text-sm text-muted-foreground mt-1">{{ webhook.name }}</p>
                  </div>

                  <div>
                    <Label class="text-sm font-medium">状态</Label>
                    <p class="text-sm text-muted-foreground mt-1">
                      {{ webhook.is_active ? '启用' : '禁用' }}
                    </p>
                  </div>

                  <div class="col-span-2">
                    <div class="flex items-center justify-between">
                      <Label class="text-sm font-medium">URL</Label>
                      <Button
                        variant="ghost"
                        size="sm"
                        @click="copyToClipboard(webhook.url, 'URL')"
                      >
                        <Copy class="h-4 w-4" />
                      </Button>
                    </div>
                    <p class="text-sm text-muted-foreground mt-1 font-mono break-all">
                      {{ webhook.url }}
                    </p>
                  </div>

                  <div>
                    <Label class="text-sm font-medium">超时时间</Label>
                    <p class="text-sm text-muted-foreground mt-1">{{ webhook.timeout }} 秒</p>
                  </div>

                  <div>
                    <Label class="text-sm font-medium">最大重试次数</Label>
                    <p class="text-sm text-muted-foreground mt-1">{{ webhook.max_attempts }} 次</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- 事件订阅 -->
            <Card>
              <CardHeader>
                <CardTitle>事件订阅</CardTitle>
              </CardHeader>
              <CardContent>
                <div class="flex flex-wrap gap-2">
                  <Badge
                    v-for="event in webhook.events"
                    :key="event"
                    variant="outline"
                    :class="{ 'border-orange-500 text-orange-700': event === '*' }"
                  >
                    {{ event === '*' ? '全部事件' : event }}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <!-- 自定义头部 -->
            <Card v-if="webhook.headers && Object.keys(webhook.headers).length > 0">
              <CardHeader>
                <CardTitle>自定义请求头</CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-2">
                  <div
                    v-for="(value, key) in webhook.headers"
                    :key="key"
                    class="flex items-center justify-between p-2 rounded border"
                  >
                    <div class="flex-1">
                      <span class="font-mono text-sm font-medium">{{ key }}:</span>
                      <span class="font-mono text-sm ml-2">{{ value }}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="copyToClipboard(`${key}: ${value}`, '请求头')"
                    >
                      <Copy class="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- 签名密钥 -->
            <Card>
              <CardHeader>
                <CardTitle>签名密钥</CardTitle>
                <CardDescription>
                  用于验证 Webhook 请求的签名密钥
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div class="flex items-center gap-2">
                  <div class="flex-1">
                    <Input
                      :type="showSecret ? 'text' : 'password'"
                      :value="webhook.secret || ''"
                      readonly
                      class="font-mono"
                    />
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    @click="showSecret = !showSecret"
                  >
                    <Eye class="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    @click="copyToClipboard(webhook.secret || '', '签名密钥')"
                    :disabled="!webhook.secret"
                  >
                    <Copy class="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <!-- 侧边栏统计 -->
      <div class="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <BarChart3 class="h-5 w-5" />
              统计概览
            </CardTitle>
            <CardDescription>
              最近 7 天的发送统计
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div class="text-center p-3 border rounded-lg">
                <div class="text-2xl font-bold">{{ stats.total }}</div>
                <div class="text-sm text-muted-foreground">总发送</div>
              </div>
              
              <div class="text-center p-3 border rounded-lg">
                <div class="text-2xl font-bold text-green-600">{{ stats.successful }}</div>
                <div class="text-sm text-muted-foreground">成功</div>
              </div>
              
              <div class="text-center p-3 border rounded-lg">
                <div class="text-2xl font-bold text-red-600">{{ stats.failed }}</div>
                <div class="text-sm text-muted-foreground">失败</div>
              </div>
              
              <div class="text-center p-3 border rounded-lg">
                <div class="text-2xl font-bold text-yellow-600">{{ stats.pending }}</div>
                <div class="text-sm text-muted-foreground">待处理</div>
              </div>
            </div>
            
            <Separator />
            
            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span>成功率</span>
                <span class="font-medium">{{ stats.success_rate }}%</span>
              </div>
              
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div 
                  class="bg-green-600 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${stats.success_rate}%` }"
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
          </CardHeader>
          <CardContent class="space-y-3 text-sm">
            <div>
              <span class="font-medium">创建时间：</span>
              <span class="text-muted-foreground">
                {{ formatDate(webhook.created_at) }}
              </span>
            </div>

            <div>
              <span class="font-medium">最后更新：</span>
              <span class="text-muted-foreground">
                {{ formatDate(webhook.updated_at) }}
              </span>
            </div>

            <div>
              <span class="font-medium">最后发送：</span>
              <span class="text-muted-foreground">
                {{ formatDate(webhook.last_delivered_at) }}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </AppLayout>
</template>
