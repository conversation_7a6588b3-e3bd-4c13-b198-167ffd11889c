# Webhook 系统扩展指南

本系统使用 `spatie/laravel-webhook-server` 库来处理 Webhook 发送，具有良好的扩展性。

## 当前架构

### 核心组件

1. **WebhookService** - 使用 spatie 库发送 webhook
2. **WebhookEventListener** - 处理 K8s 事件
3. **GenericWebhookListener** - 处理任何有 `getWebhookPayload` 方法的事件
4. **WebhookCallEventListener** - 监听 spatie 库的事件，更新发送状态

### 事件流程

```
事件触发 → 监听器 → WebhookService → spatie库 → HTTP请求 → 状态更新
```

## 如何添加新的事件类型

### 1. 创建事件类

```php
<?php

namespace App\Events\User;

use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;

class UserRegistered
{
    use Dispatchable;

    public function __construct(public User $user) {}

    /**
     * 实现此方法以支持 Webhook
     */
    public function getWebhookPayload(): array
    {
        return [
            'event_id' => uniqid(),
            'event_type' => 'user.registered',
            'user' => [
                'id' => $this->user->id,
                'name' => $this->user->name,
                'email' => $this->user->email,
            ],
            'timestamp' => now()->toISOString(),
        ];
    }
}
```

### 2. 注册事件监听器

在 `app/Providers/EventServiceProvider.php` 中添加：

```php
protected $listen = [
    // 现有的事件...
    
    // 新的事件
    UserRegistered::class => [
        GenericWebhookListener::class,
    ],
];
```

### 3. 更新 Webhook 端点验证规则

在 `app/Http/Requests/StoreWebhookEndpointRequest.php` 和 `UpdateWebhookEndpointRequest.php` 中添加新的事件类型：

```php
$availableEvents = [
    '*',
    // K8s 事件...
    
    // 新的事件类型
    'user.registered',
    'user.updated',
    'billing.payment_processed',
];
```

### 4. 触发事件

```php
// 在适当的地方触发事件
event(new UserRegistered($user));
```

## 工作区关联

### K8s 事件
通过 `namespace` 属性自动关联到对应的工作区。

### 用户事件
可以通过以下方式关联工作区：

1. **添加 workspaceId 属性**：
```php
public function __construct(
    public User $user,
    public ?int $workspaceId = null
) {}
```

2. **添加 namespace 属性**：
```php
public function __construct(
    public User $user,
    public ?string $namespace = null
) {}
```

3. **自定义查找逻辑**：
修改 `GenericWebhookListener::findMatchingEndpoints` 方法。

## 配置 spatie/laravel-webhook-server

在 `config/webhook-server.php` 中可以配置：

- 重试次数
- 超时时间
- 退避策略
- 队列设置

## 监控和调试

### 日志
所有 Webhook 相关的日志都会记录在 Laravel 日志中，包括：
- 事件处理
- 发送状态
- 错误信息

### 数据库记录
`webhook_deliveries` 表记录了所有 Webhook 发送的详细信息。

### 事件
系统会触发以下事件：
- `WebhookCallSucceededEvent` - 发送成功
- `WebhookCallFailedEvent` - 发送失败（会重试）
- `FinalWebhookCallFailedEvent` - 最终失败

## 最佳实践

1. **保持负载数据简洁** - 只包含必要的信息
2. **使用唯一的事件类型名称** - 避免冲突
3. **合理设置工作区关联** - 确保 Webhook 发送到正确的端点
4. **处理敏感数据** - 不要在负载中包含密码等敏感信息
5. **测试事件** - 使用 `WebhookService::testEndpoint()` 测试端点

## 示例：计费事件

```php
<?php

namespace App\Events\Billing;

use App\Models\User;

class PaymentProcessed
{
    use Dispatchable;

    public function __construct(
        public User $user,
        public string $amount,
        public string $currency,
        public ?int $workspaceId = null
    ) {}

    public function getWebhookPayload(): array
    {
        return [
            'event_id' => uniqid(),
            'event_type' => 'billing.payment_processed',
            'user' => [
                'id' => $this->user->id,
                'email' => $this->user->email,
            ],
            'payment' => [
                'amount' => $this->amount,
                'currency' => $this->currency,
            ],
            'workspace_id' => $this->workspaceId,
            'timestamp' => now()->toISOString(),
        ];
    }
}
```

这样的设计既保持了简单性，又具有良好的扩展性。
