# Webhook 系统重构文档

## 概述

本次重构将原有的过度设计的 Webhook 系统改造为基于 Laravel Notification 的现代化架构，充分利用了 Laravel 的内置特性，提高了系统的可维护性和扩展性。

## 重构目标

1. **消除重复代码**：统一事件定义，避免在多个请求类中重复定义事件列表
2. **提高可扩展性**：使用统一的事件注册系统，便于添加新的事件类型
3. **简化架构**：使用 Laravel Notification 系统替代自定义的 Webhook 服务
4. **增强通用性**：不再特定于 K8s 事件，支持任意类型的事件

## 新架构组件

### 1. 事件注册系统

#### WebhookEventRegistry (`app/Services/WebhookEventRegistry.php`)
- 统一管理所有可用的 Webhook 事件类型
- 支持事件分组和描述
- 提供验证用的事件类型列表

#### WebhookServiceProvider (`app/Providers/WebhookServiceProvider.php`)
- 注册 WebhookEventRegistry 为单例服务
- 自动注册默认事件（K8s、系统、用户等）
- 支持从配置文件加载自定义事件

### 2. 通知系统

#### WebhookChannel (`app/Notifications/Channels/WebhookChannel.php`)
- Laravel 通知渠道实现
- 自动查找匹配的 Webhook 端点
- 使用 spatie/laravel-webhook-server 发送请求

#### WebhookNotification (`app/Notifications/WebhookNotification.php`)
- 抽象基类，定义 Webhook 通知的基本结构
- 支持自定义负载构建
- 集成配置文件设置

#### K8sResourceWebhookNotification (`app/Notifications/K8sResourceWebhookNotification.php`)
- 专门处理 K8s 资源事件的通知类
- 从 BaseK8sResourceEvent 创建通知
- 定制化的负载结构

#### GenericWebhookNotification (`app/Notifications/GenericWebhookNotification.php`)
- 通用事件通知类
- 支持自定义负载构建器
- 提供便捷的创建方法

### 3. 监听器

#### WebhookNotificationListener (`app/Listeners/WebhookNotificationListener.php`)
- 通用事件监听器，替代原有的多个专用监听器
- 自动识别事件类型
- 智能确定通知目标

### 4. 辅助组件

#### HasWebhookNotifications (`app/Traits/HasWebhookNotifications.php`)
- 为模型提供 Webhook 通知功能
- 简化通知发送接口
- 自动确定工作区关联

#### 配置文件 (`config/webhooks.php`)
- 集中管理 Webhook 相关配置
- 支持自定义事件定义
- 负载格式和安全设置

## 使用方法

### 发送 Webhook 通知

```php
// 使用 Workspace 模型发送通知
$workspace = Workspace::find(1);
$workspace->notifyWebhook('deployment.created', [
    'deployment_name' => 'my-app',
    'namespace' => 'production'
]);

// 使用自定义通知
$notification = new GenericWebhookNotification('user.created', [
    'user_id' => 123,
    'email' => '<EMAIL>'
]);
$workspace->notify($notification);

// 从 K8s 事件创建通知
$notification = K8sResourceWebhookNotification::fromK8sEvent($k8sEvent);
$workspace->notify($notification);
```

### 注册新事件类型

```php
// 在 WebhookServiceProvider 中
$registry->register('custom.event', 'Custom Event Description', 'custom');

// 或在配置文件中
'events' => [
    'custom' => [
        'billing.invoice_created' => '账单创建',
        'billing.payment_received' => '付款接收',
    ],
],
```

### 测试 Webhook 系统

```bash
# 列出所有注册的事件
php artisan webhook:test-new --list-events

# 测试特定工作区的 Webhook
php artisan webhook:test-new --workspace=1 --event=test

# 迁移检查
php artisan webhook:migrate --dry-run
```

## 向后兼容性

- 保持现有的数据库结构不变
- WebhookEndpoint 和 WebhookDelivery 模型继续使用
- 现有的 API 端点继续工作
- 旧的 WebhookService 标记为 deprecated 但仍可使用

## 迁移步骤

1. **验证新系统**：运行 `php artisan webhook:migrate --dry-run`
2. **测试功能**：使用 `php artisan webhook:test-new` 测试
3. **更新代码**：逐步将旧的 WebhookService 调用替换为新的通知系统
4. **清理旧文件**：运行 `php artisan webhook:migrate --remove-old`

## 优势

1. **更好的 Laravel 集成**：使用标准的 Notification 系统
2. **更少的重复代码**：统一的事件注册和验证
3. **更强的扩展性**：易于添加新的事件类型和通知渠道
4. **更好的测试性**：每个组件都可以独立测试
5. **更清晰的架构**：职责分离，代码更易理解

## 配置选项

新系统提供了丰富的配置选项：

- 事件定义和分组
- 负载格式设置
- 安全配置
- 默认超时和重试设置

详见 `config/webhooks.php` 文件。

## 总结

这次重构成功地将一个过度设计的系统转换为一个现代化、可维护的 Laravel 应用组件。新系统不仅解决了原有的问题，还为未来的扩展提供了坚实的基础。
