<?php

namespace App\Traits;

use App\Notifications\GenericWebhookNotification;
use App\Notifications\WebhookNotification;

trait HasWebhookNotifications
{
    /**
     * 发送 Webhook 通知
     */
    public function notifyWebhook(string $eventType, array $data = []): void
    {
        $notification = new GenericWebhookNotification($eventType, $data);
        $this->notify($notification);
    }

    /**
     * 发送自定义 Webhook 通知
     */
    public function notifyWebhookWith(WebhookNotification $notification): void
    {
        $this->notify($notification);
    }

    /**
     * 获取用于 Webhook 的工作区 ID
     * 子类可以重写此方法来提供自定义逻辑
     */
    public function getWebhookWorkspaceId(): ?int
    {
        // 如果模型直接有 workspace_id 属性
        if (property_exists($this, 'workspace_id')) {
            return $this->workspace_id;
        }

        // 如果模型有 workspace 关系
        if (method_exists($this, 'workspace')) {
            $workspace = $this->workspace;
            return $workspace ? $workspace->id : null;
        }

        // 如果是 Workspace 模型本身
        if (get_class($this) === 'App\Models\Workspace') {
            return $this->id;
        }

        return null;
    }

    /**
     * 获取 Webhook 通知的路由键
     * 默认返回模型的主键
     */
    public function routeNotificationForWebhook(): string
    {
        return (string) $this->getKey();
    }
}
