<?php

namespace App\Services;

use Illuminate\Support\Collection;

class WebhookEventRegistry
{
    /**
     * 所有注册的 Webhook 事件
     */
    private array $events = [];

    /**
     * 事件分组
     */
    private array $groups = [];

    /**
     * 注册一个 Webhook 事件
     */
    public function register(string $eventType, string $description = '', string $group = 'general'): self
    {
        $this->events[$eventType] = [
            'type' => $eventType,
            'description' => $description,
            'group' => $group,
        ];

        if (!isset($this->groups[$group])) {
            $this->groups[$group] = [];
        }

        $this->groups[$group][] = $eventType;

        return $this;
    }

    /**
     * 批量注册事件
     */
    public function registerMany(array $events, string $group = 'general'): self
    {
        foreach ($events as $eventType => $description) {
            if (is_numeric($eventType)) {
                // 如果是数字索引，说明只有事件类型没有描述
                $this->register($description, '', $group);
            } else {
                $this->register($eventType, $description, $group);
            }
        }

        return $this;
    }

    /**
     * 注册 K8s 资源事件
     */
    public function registerK8sEvents(): self
    {
        $k8sResources = [
            'deployment' => 'Kubernetes Deployment 资源',
            'statefulset' => 'Kubernetes StatefulSet 资源',
            'service' => 'Kubernetes Service 资源',
            'pod' => 'Kubernetes Pod 资源',
            'secret' => 'Kubernetes Secret 资源',
            'configmap' => 'Kubernetes ConfigMap 资源',
            'ingress' => 'Kubernetes Ingress 资源',
            'persistentvolumeclaim' => 'Kubernetes PVC 资源',
            'horizontalpodautoscaler' => 'Kubernetes HPA 资源',
            'event' => 'Kubernetes Event 资源',
        ];

        $actions = [
            'created' => '创建',
            'updated' => '更新',
            'deleted' => '删除',
            'scaled' => '扩缩容',
        ];

        foreach ($k8sResources as $resource => $resourceDesc) {
            foreach ($actions as $action => $actionDesc) {
                // 跳过某些资源不支持的操作
                if ($resource === 'event' && $action === 'scaled') {
                    continue;
                }
                if (in_array($resource, ['secret', 'configmap', 'persistentvolumeclaim', 'horizontalpodautoscaler']) && $action === 'scaled') {
                    continue;
                }

                $eventType = "{$resource}.{$action}";
                $description = "{$resourceDesc} - {$actionDesc}";
                $this->register($eventType, $description, 'k8s');
            }
        }

        return $this;
    }

    /**
     * 获取所有注册的事件
     */
    public function getAllEvents(): array
    {
        return $this->events;
    }

    /**
     * 获取所有事件类型（用于验证）
     */
    public function getAllEventTypes(): array
    {
        return array_keys($this->events);
    }

    /**
     * 获取按分组组织的事件
     */
    public function getEventsByGroup(): array
    {
        $result = [];
        foreach ($this->groups as $group => $eventTypes) {
            $result[$group] = array_map(function ($eventType) {
                return $this->events[$eventType];
            }, $eventTypes);
        }
        return $result;
    }

    /**
     * 获取指定分组的事件
     */
    public function getEventsInGroup(string $group): array
    {
        if (!isset($this->groups[$group])) {
            return [];
        }

        return array_map(function ($eventType) {
            return $this->events[$eventType];
        }, $this->groups[$group]);
    }

    /**
     * 检查事件是否已注册
     */
    public function isRegistered(string $eventType): bool
    {
        return isset($this->events[$eventType]);
    }

    /**
     * 获取事件信息
     */
    public function getEvent(string $eventType): ?array
    {
        return $this->events[$eventType] ?? null;
    }

    /**
     * 获取用于表单验证的事件列表（包含通配符）
     */
    public function getValidationEventTypes(): array
    {
        return array_merge(['*'], $this->getAllEventTypes());
    }

    /**
     * 清空所有注册的事件（主要用于测试）
     */
    public function clear(): self
    {
        $this->events = [];
        $this->groups = [];
        return $this;
    }
}
