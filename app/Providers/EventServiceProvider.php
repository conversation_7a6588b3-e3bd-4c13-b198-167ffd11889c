<?php

namespace App\Providers;

use App\Listeners\WebhookCallEventListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Spa<PERSON>\WebhookServer\Events\FinalWebhookCallFailedEvent;
use Spa<PERSON>\WebhookServer\Events\WebhookCallFailedEvent;
use Spatie\WebhookServer\Events\WebhookCallSucceededEvent;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        // spatie/laravel-webhook-server 事件监听器
        WebhookCallSucceededEvent::class => [
            WebhookCallEventListener::class . '@handleWebhookCallSucceeded',
        ],
        WebhookCallFailedEvent::class => [
            WebhookCallEventListener::class . '@handleWebhookCallFailed',
        ],
        FinalWebhookCallFailedEvent::class => [
            WebhookCallEventListener::class . '@handleFinalWebhookCallFailed',
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return true;
    }
}
