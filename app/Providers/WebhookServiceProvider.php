<?php

namespace App\Providers;

use App\Services\WebhookEventRegistry;
use Illuminate\Support\ServiceProvider;

class WebhookServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // 注册 WebhookEventRegistry 为单例
        $this->app->singleton(WebhookEventRegistry::class, function ($app) {
            $registry = new WebhookEventRegistry();
            
            // 注册默认事件
            $this->registerDefaultEvents($registry);
            
            return $registry;
        });

        // 创建别名以便更容易访问
        $this->app->alias(WebhookEventRegistry::class, 'webhook.events');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }

    /**
     * 注册默认的 Webhook 事件
     */
    private function registerDefaultEvents(WebhookEventRegistry $registry): void
    {
        // 注册 K8s 相关事件
        $registry->registerK8sEvents();

        // 注册系统事件
        $registry->registerMany([
            'test' => '测试事件',
            'system.maintenance' => '系统维护事件',
            'system.alert' => '系统告警事件',
        ], 'system');

        // 注册用户相关事件
        $registry->registerMany([
            'user.created' => '用户创建',
            'user.updated' => '用户更新',
            'user.deleted' => '用户删除',
        ], 'user');

        // 注册工作区相关事件
        $registry->registerMany([
            'workspace.created' => '工作区创建',
            'workspace.updated' => '工作区更新',
            'workspace.deleted' => '工作区删除',
        ], 'workspace');

        // 可以在这里添加更多事件类型
        // 或者通过配置文件来定义事件
        $this->registerConfigEvents($registry);
    }

    /**
     * 从配置文件注册事件
     */
    private function registerConfigEvents(WebhookEventRegistry $registry): void
    {
        $configEvents = config('webhooks.events', []);
        
        foreach ($configEvents as $group => $events) {
            if (is_array($events)) {
                $registry->registerMany($events, $group);
            }
        }
    }
}
