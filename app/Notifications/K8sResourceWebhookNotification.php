<?php

namespace App\Notifications;

class K8sResourceWebhookNotification extends WebhookNotification
{
    /**
     * 构建 K8s 资源事件的 Webhook 负载
     */
    protected function buildPayload(object $notifiable): array
    {
        $basePayload = parent::buildPayload($notifiable);

        // 为 K8s 资源事件定制负载结构
        $k8sPayload = [
            'event_id' => $basePayload['event_id'] ?? uniqid(),
            'event_type' => $this->eventType,
            'namespace' => $this->eventData['namespace'] ?? null,
            'cluster' => [
                'id' => $this->eventData['cluster_id'] ?? null,
                'name' => $this->eventData['cluster_name'] ?? null,
            ],
            'resource' => [
                'type' => $this->eventData['resource_type'] ?? null,
                'name' => $this->eventData['resource_name'] ?? null,
                'data' => $this->eventData['resource'] ?? [],
            ],
            'action' => $this->eventData['action'] ?? null,
            'timestamp' => $basePayload['timestamp'] ?? now()->toISOString(),
        ];

        // 添加发送者信息（如果配置启用）
        if (config('webhooks.payload.include_sender', true)) {
            $k8sPayload['sender'] = $basePayload['sender'];
        }

        return $k8sPayload;
    }

    /**
     * 从 BaseK8sResourceEvent 创建通知
     */
    public static function fromK8sEvent($event): self
    {
        $eventType = "{$event->resourceType}.{$event->action}";
        
        $eventData = [
            'namespace' => $event->namespace,
            'cluster_id' => $event->clusterId,
            'cluster_name' => $event->clusterName,
            'resource_type' => $event->resourceType,
            'resource_name' => $event->resourceName,
            'resource' => $event->resource,
            'action' => $event->action,
        ];

        return new self($eventType, $eventData);
    }
}
