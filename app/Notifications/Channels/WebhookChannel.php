<?php

namespace App\Notifications\Channels;

use App\Models\WebhookDelivery;
use App\Models\WebhookEndpoint;
use App\Notifications\WebhookNotification;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\WebhookServer\WebhookCall;

class WebhookChannel
{
    /**
     * Send the given notification.
     */
    public function send(object $notifiable, Notification $notification): void
    {
        if (!$notification instanceof WebhookNotification) {
            Log::warning('WebhookChannel can only send WebhookNotification instances', [
                'notification_class' => get_class($notification),
            ]);
            return;
        }

        // 获取 Webhook 数据
        $webhookData = $notification->toWebhook($notifiable);
        
        if (!$webhookData) {
            Log::warning('Webhook notification returned no data', [
                'notification_class' => get_class($notification),
                'notifiable_type' => get_class($notifiable),
                'notifiable_id' => $notifiable->getKey(),
            ]);
            return;
        }

        // 获取要发送的端点
        $endpoints = $this->getWebhookEndpoints($notifiable, $webhookData['event_type']);

        if ($endpoints->isEmpty()) {
            Log::debug('No webhook endpoints found for notification', [
                'event_type' => $webhookData['event_type'],
                'notifiable_type' => get_class($notifiable),
            ]);
            return;
        }

        // 向每个端点发送 Webhook
        foreach ($endpoints as $endpoint) {
            $this->sendToEndpoint($endpoint, $webhookData);
        }
    }

    /**
     * 获取应该接收此通知的 Webhook 端点
     */
    private function getWebhookEndpoints(object $notifiable, string $eventType): \Illuminate\Support\Collection
    {
        $query = WebhookEndpoint::where('is_active', true);

        // 根据 notifiable 类型确定查询条件
        if (method_exists($notifiable, 'getWebhookWorkspaceId')) {
            // 如果 notifiable 有获取工作区 ID 的方法
            $workspaceId = $notifiable->getWebhookWorkspaceId();
            if ($workspaceId) {
                $query->where('workspace_id', $workspaceId);
            }
        } elseif (property_exists($notifiable, 'workspace_id')) {
            // 如果 notifiable 直接有 workspace_id 属性
            $query->where('workspace_id', $notifiable->workspace_id);
        } elseif (method_exists($notifiable, 'workspace')) {
            // 如果 notifiable 有 workspace 关系
            $workspace = $notifiable->workspace;
            if ($workspace) {
                $query->where('workspace_id', $workspace->id);
            }
        }

        return $query->get()->filter(function (WebhookEndpoint $endpoint) use ($eventType) {
            return $endpoint->shouldReceiveEvent($eventType) || $endpoint->shouldReceiveEvent('*');
        });
    }

    /**
     * 向指定端点发送 Webhook
     */
    private function sendToEndpoint(WebhookEndpoint $endpoint, array $webhookData): void
    {
        // 创建 delivery 记录
        $delivery = WebhookDelivery::create([
            'webhook_endpoint_id' => $endpoint->id,
            'url' => $endpoint->url,
            'event_type' => $webhookData['event_type'],
            'payload' => $webhookData['payload'],
            'headers' => $endpoint->headers ?? [],
            'status' => 'pending',
        ]);

        try {
            // 使用 spatie/laravel-webhook-server 发送 webhook
            $webhookCall = WebhookCall::create()
                ->url($endpoint->url)
                ->payload($webhookData['payload'])
                ->timeoutInSeconds($endpoint->timeout)
                ->maximumTries($endpoint->max_attempts)
                ->withHeaders(array_merge(
                    config('webhooks.defaults.headers', []),
                    $endpoint->headers ?? []
                ))
                ->meta([
                    'delivery_id' => $delivery->id,
                    'endpoint_id' => $endpoint->id,
                    'event_type' => $webhookData['event_type'],
                ]);

            // 如果有 secret，使用签名
            if ($endpoint->secret) {
                $webhookCall->useSecret($endpoint->secret);
            } else {
                $webhookCall->doNotSign();
            }

            // 异步发送
            $webhookCall->dispatch();

            Log::info('Webhook notification dispatched successfully', [
                'endpoint_id' => $endpoint->id,
                'delivery_id' => $delivery->id,
                'event_type' => $webhookData['event_type'],
            ]);

            // 更新 endpoint 的最后发送时间
            $endpoint->update(['last_delivered_at' => now()]);

        } catch (\Exception $e) {
            $delivery->markAsFailed($e->getMessage());

            Log::error('Webhook notification dispatch failed', [
                'endpoint_id' => $endpoint->id,
                'delivery_id' => $delivery->id,
                'event_type' => $webhookData['event_type'],
                'error' => $e->getMessage(),
            ]);
        }
    }
}
