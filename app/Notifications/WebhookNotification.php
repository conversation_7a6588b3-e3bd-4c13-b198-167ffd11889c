<?php

namespace App\Notifications;

use App\Notifications\Channels\WebhookChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

abstract class WebhookNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * 事件类型
     */
    protected string $eventType;

    /**
     * 事件数据
     */
    protected array $eventData;

    /**
     * 创建通知实例
     */
    public function __construct(string $eventType, array $eventData = [])
    {
        $this->eventType = $eventType;
        $this->eventData = $eventData;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return [WebhookChannel::class];
    }

    /**
     * Get the webhook representation of the notification.
     */
    public function toWebhook(object $notifiable): ?array
    {
        $payload = $this->buildPayload($notifiable);
        
        if (!$payload) {
            return null;
        }

        return [
            'event_type' => $this->eventType,
            'payload' => $payload,
        ];
    }

    /**
     * 构建 Webhook 负载数据
     * 子类应该重写此方法来定制负载结构
     */
    protected function buildPayload(object $notifiable): array
    {
        $payload = [
            'event_type' => $this->eventType,
            'data' => $this->eventData,
        ];

        // 添加配置中的默认字段
        if (config('webhooks.payload.include_event_id', true)) {
            $payload['event_id'] = uniqid();
        }

        if (config('webhooks.payload.include_timestamp', true)) {
            $format = config('webhooks.payload.timestamp_format', 'c');
            $payload['timestamp'] = now()->format($format);
        }

        if (config('webhooks.payload.include_sender', true)) {
            $payload['sender'] = config('webhooks.payload.sender', [
                'name' => 'PaaS Platform',
                'version' => '1.0',
            ]);
        }

        return $payload;
    }

    /**
     * 获取事件类型
     */
    public function getEventType(): string
    {
        return $this->eventType;
    }

    /**
     * 获取事件数据
     */
    public function getEventData(): array
    {
        return $this->eventData;
    }

    /**
     * 设置事件数据
     */
    public function setEventData(array $data): self
    {
        $this->eventData = $data;
        return $this;
    }

    /**
     * 添加事件数据
     */
    public function addEventData(string $key, mixed $value): self
    {
        $this->eventData[$key] = $value;
        return $this;
    }
}
