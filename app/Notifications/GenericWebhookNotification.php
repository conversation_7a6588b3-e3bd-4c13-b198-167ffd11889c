<?php

namespace App\Notifications;

class GenericWebhookNotification extends WebhookNotification
{
    /**
     * 自定义负载构建器
     */
    private ?\Closure $payloadBuilder = null;

    /**
     * 设置自定义负载构建器
     */
    public function withPayloadBuilder(\Closure $builder): self
    {
        $this->payloadBuilder = $builder;
        return $this;
    }

    /**
     * 构建通用事件的 Webhook 负载
     */
    protected function buildPayload(object $notifiable): array
    {
        // 如果有自定义负载构建器，使用它
        if ($this->payloadBuilder) {
            $customPayload = ($this->payloadBuilder)($notifiable, $this->eventType, $this->eventData);
            if (is_array($customPayload)) {
                return $customPayload;
            }
        }

        // 否则使用默认构建逻辑
        return parent::buildPayload($notifiable);
    }

    /**
     * 创建一个简单的通知实例
     */
    public static function create(string $eventType, array $data = []): self
    {
        return new self($eventType, $data);
    }

    /**
     * 创建一个测试通知
     */
    public static function test(object $notifiable): self
    {
        $data = [
            'message' => 'This is a test webhook from PaaS',
            'notifiable_type' => get_class($notifiable),
            'notifiable_id' => $notifiable->getKey(),
        ];

        return new self('test', $data);
    }
}
