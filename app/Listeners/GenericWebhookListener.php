<?php

namespace App\Listeners;

use App\Models\WebhookEndpoint;
use App\Service\WebhookService;
use Illuminate\Support\Facades\Log;

/**
 * 通用 Webhook 监听器
 * 处理任何有 getWebhookPayload 方法的事件
 * 
 * 使用方法：
 * 1. 在事件类中实现 getWebhookPayload() 方法
 * 2. 在 EventServiceProvider 中注册事件监听器
 * 
 * 示例：
 * protected $listen = [
 *     UserRegistered::class => [
 *         GenericWebhookListener::class,
 *     ],
 * ];
 */
class GenericWebhookListener
{
    public function __construct(
        private WebhookService $webhookService
    ) {}

    /**
     * 处理事件
     */
    public function handle($event): void
    {
        // 检查事件是否有 getWebhookPayload 方法
        if (!method_exists($event, 'getWebhookPayload')) {
            Log::debug('Event does not have getWebhookPayload method, skipping webhook', [
                'event_class' => get_class($event),
            ]);
            return;
        }

        try {
            $payload = $event->getWebhookPayload();
            $eventType = $payload['event_type'] ?? 'unknown';

            Log::debug('Processing generic webhook event', [
                'event_type' => $eventType,
                'event_class' => get_class($event),
            ]);

            // 查找匹配的 Webhook 端点
            $webhookEndpoints = $this->findMatchingEndpoints($event, $eventType);

            if ($webhookEndpoints->isEmpty()) {
                Log::debug('No matching webhook endpoints found', [
                    'event_type' => $eventType,
                    'event_class' => get_class($event),
                ]);
                return;
            }

            // 为每个匹配的端点发送 Webhook
            foreach ($webhookEndpoints as $endpoint) {
                if ($this->shouldSendToEndpoint($endpoint, $eventType)) {
                    Log::debug('Sending webhook for generic event', [
                        'endpoint_id' => $endpoint->id,
                        'endpoint_name' => $endpoint->name,
                        'event_type' => $eventType,
                        'event_class' => get_class($event),
                    ]);

                    $this->webhookService->sendWebhook($endpoint, $eventType, $payload);
                }
            }

        } catch (\Exception $e) {
            Log::error('Error processing generic webhook event', [
                'error' => $e->getMessage(),
                'event_class' => get_class($event),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 查找匹配的 Webhook 端点
     */
    private function findMatchingEndpoints($event, string $eventType): \Illuminate\Database\Eloquent\Collection
    {
        $query = WebhookEndpoint::where('is_active', true);

        // 如果事件有 namespace 属性，通过 workspace 关联查找
        if (property_exists($event, 'namespace') && $event->namespace) {
            $query->whereHas('workspace', function ($q) use ($event) {
                $q->where('namespace', $event->namespace);
            });
        }
        // 如果事件有 workspaceId 属性，直接查找
        elseif (property_exists($event, 'workspaceId') && $event->workspaceId) {
            $query->where('workspace_id', $event->workspaceId);
        }
        // 如果事件有 user 属性，可以通过用户的工作区查找
        elseif (property_exists($event, 'user') && $event->user) {
            // 这里可以根据业务逻辑决定如何处理用户相关事件
            // 例如：发送到用户的所有工作区，或者发送到全局端点
            Log::debug('User-related event, consider implementing workspace lookup logic', [
                'event_type' => $eventType,
                'user_id' => $event->user->id ?? null,
            ]);
            return collect(); // 暂时返回空集合
        }
        // 其他情况，可以考虑发送到全局端点（如果有的话）
        else {
            Log::debug('Event has no workspace context, skipping', [
                'event_type' => $eventType,
            ]);
            return collect(); // 暂时返回空集合
        }

        return $query->get();
    }

    /**
     * 检查是否应该向端点发送事件
     */
    private function shouldSendToEndpoint(WebhookEndpoint $endpoint, string $eventType): bool
    {
        return $endpoint->shouldReceiveEvent($eventType) || $endpoint->shouldReceiveEvent('*');
    }

    /**
     * 处理任务失败
     */
    public function failed($event, \Throwable $exception): void
    {
        $eventType = 'unknown';
        if (method_exists($event, 'getWebhookPayload')) {
            try {
                $payload = $event->getWebhookPayload();
                $eventType = $payload['event_type'] ?? 'unknown';
            } catch (\Exception $e) {
                // 忽略获取事件类型时的错误
            }
        }

        Log::error('Generic webhook listener failed', [
            'event_type' => $eventType,
            'event_class' => get_class($event),
            'error' => $exception->getMessage(),
        ]);
    }
}
