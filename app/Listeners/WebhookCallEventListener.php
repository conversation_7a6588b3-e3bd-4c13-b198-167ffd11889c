<?php

namespace App\Listeners;

use App\Models\WebhookDelivery;
use Illuminate\Support\Facades\Log;
use Spa<PERSON>\WebhookServer\Events\FinalWebhookCallFailedEvent;
use Spatie\WebhookServer\Events\WebhookCallFailedEvent;
use Spa<PERSON>\WebhookServer\Events\WebhookCallSucceededEvent;

/**
 * 监听 spatie/laravel-webhook-server 的事件
 * 用于更新我们的 WebhookDelivery 记录
 */
class WebhookCallEventListener
{
    /**
     * 处理 webhook 调用成功事件
     */
    public function handleWebhookCallSucceeded(WebhookCallSucceededEvent $event): void
    {
        $deliveryId = $event->meta['delivery_id'] ?? null;
        
        if (!$deliveryId) {
            return;
        }

        $delivery = WebhookDelivery::find($deliveryId);
        
        if (!$delivery) {
            Log::warning('WebhookDelivery not found for successful webhook call', [
                'delivery_id' => $deliveryId,
                'webhook_url' => $event->webhookUrl,
            ]);
            return;
        }

        // 更新为成功状态
        $delivery->markAsSuccessful(
            $event->response?->getStatusCode() ?? 200,
            $event->response?->getBody()?->getContents() ?? ''
        );

        Log::info('Webhook delivery marked as successful', [
            'delivery_id' => $deliveryId,
            'endpoint_id' => $event->meta['endpoint_id'] ?? null,
            'event_type' => $event->meta['event_type'] ?? null,
            'attempt' => $event->attempt,
        ]);
    }

    /**
     * 处理 webhook 调用失败事件（会重试）
     */
    public function handleWebhookCallFailed(WebhookCallFailedEvent $event): void
    {
        $deliveryId = $event->meta['delivery_id'] ?? null;
        
        if (!$deliveryId) {
            return;
        }

        $delivery = WebhookDelivery::find($deliveryId);
        
        if (!$delivery) {
            Log::warning('WebhookDelivery not found for failed webhook call', [
                'delivery_id' => $deliveryId,
                'webhook_url' => $event->webhookUrl,
            ]);
            return;
        }

        // 获取错误信息
        $errorMessage = $this->getErrorMessage($event);
        
        // 计算下次重试时间（spatie 库会自动处理重试）
        $nextRetryIn = $this->calculateNextRetryDelay($event->attempt);

        $delivery->markAsFailed($errorMessage, $nextRetryIn);

        Log::warning('Webhook delivery failed, will retry', [
            'delivery_id' => $deliveryId,
            'endpoint_id' => $event->meta['endpoint_id'] ?? null,
            'event_type' => $event->meta['event_type'] ?? null,
            'attempt' => $event->attempt,
            'error' => $errorMessage,
            'next_retry_in' => $nextRetryIn,
        ]);
    }

    /**
     * 处理 webhook 最终失败事件（不再重试）
     */
    public function handleFinalWebhookCallFailed(FinalWebhookCallFailedEvent $event): void
    {
        $deliveryId = $event->meta['delivery_id'] ?? null;
        
        if (!$deliveryId) {
            return;
        }

        $delivery = WebhookDelivery::find($deliveryId);
        
        if (!$delivery) {
            Log::warning('WebhookDelivery not found for final failed webhook call', [
                'delivery_id' => $deliveryId,
                'webhook_url' => $event->webhookUrl,
            ]);
            return;
        }

        // 获取错误信息
        $errorMessage = $this->getErrorMessage($event);

        // 标记为最终失败（不再重试）
        $delivery->markAsFailed($errorMessage, null);

        Log::error('Webhook delivery failed permanently', [
            'delivery_id' => $deliveryId,
            'endpoint_id' => $event->meta['endpoint_id'] ?? null,
            'event_type' => $event->meta['event_type'] ?? null,
            'attempt' => $event->attempt,
            'error' => $errorMessage,
        ]);
    }

    /**
     * 从事件中提取错误信息
     */
    private function getErrorMessage($event): string
    {
        if ($event->response) {
            return "HTTP {$event->response->getStatusCode()}: " . 
                   ($event->response->getBody()?->getContents() ?? 'No response body');
        }

        // 如果没有响应，可能是连接超时或其他网络错误
        return 'Connection failed or timeout';
    }

    /**
     * 计算下次重试延迟时间（指数退避）
     */
    private function calculateNextRetryDelay(int $attempt): int
    {
        // 指数退避：10秒、100秒、1000秒等（与 spatie 库默认策略一致）
        return min(10 * pow(10, $attempt - 1), 100000);
    }
}
