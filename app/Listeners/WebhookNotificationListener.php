<?php

namespace App\Listeners;

use App\Events\K8s\BaseK8sResourceEvent;
use App\Models\Workspace;
use App\Notifications\K8sResourceWebhookNotification;
use App\Notifications\GenericWebhookNotification;
use App\Services\WebhookEventRegistry;
use Illuminate\Support\Facades\Log;

class WebhookNotificationListener
{
    public function __construct(
        private WebhookEventRegistry $eventRegistry
    ) {}

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        try {
            // 确定事件类型
            $eventType = $this->determineEventType($event);
            
            if (!$eventType) {
                Log::debug('Could not determine event type for webhook notification', [
                    'event_class' => get_class($event),
                ]);
                return;
            }

            // 检查事件是否已注册
            if (!$this->eventRegistry->isRegistered($eventType)) {
                Log::debug('Event type not registered for webhook notifications', [
                    'event_type' => $eventType,
                    'event_class' => get_class($event),
                ]);
                return;
            }

            // 获取通知目标
            $notifiable = $this->getNotifiable($event);
            
            if (!$notifiable) {
                Log::debug('Could not determine notifiable for webhook event', [
                    'event_type' => $eventType,
                    'event_class' => get_class($event),
                ]);
                return;
            }

            // 创建并发送通知
            $notification = $this->createNotification($event, $eventType);
            
            if ($notification) {
                $notifiable->notify($notification);
                
                Log::debug('Webhook notification sent successfully', [
                    'event_type' => $eventType,
                    'notifiable_type' => get_class($notifiable),
                    'notifiable_id' => $notifiable->getKey(),
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error processing webhook notification', [
                'error' => $e->getMessage(),
                'event_class' => get_class($event),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 确定事件类型
     */
    private function determineEventType(object $event): ?string
    {
        // 处理 K8s 资源事件
        if ($event instanceof BaseK8sResourceEvent) {
            return "{$event->resourceType}.{$event->action}";
        }

        // 处理其他事件类型
        // 可以根据事件类名或属性来确定事件类型
        $eventClass = get_class($event);
        
        // 尝试从事件类名推断事件类型
        if (property_exists($event, 'eventType')) {
            return $event->eventType;
        }

        // 从类名推断（例如：UserCreatedEvent -> user.created）
        if (preg_match('/([A-Z][a-z]+)([A-Z][a-z]+)Event$/', class_basename($eventClass), $matches)) {
            $resource = strtolower($matches[1]);
            $action = strtolower($matches[2]);
            return "{$resource}.{$action}";
        }

        return null;
    }

    /**
     * 获取通知目标
     */
    private function getNotifiable(object $event): ?object
    {
        // 处理 K8s 资源事件
        if ($event instanceof BaseK8sResourceEvent) {
            // 通过 namespace 查找对应的 workspace
            return Workspace::where('namespace', $event->namespace)->first();
        }

        // 处理其他事件类型
        if (property_exists($event, 'workspace')) {
            return $event->workspace;
        }

        if (property_exists($event, 'user')) {
            return $event->user;
        }

        // 如果事件本身就是 notifiable
        if (method_exists($event, 'notify')) {
            return $event;
        }

        return null;
    }

    /**
     * 创建通知实例
     */
    private function createNotification(object $event, string $eventType): ?object
    {
        // 为 K8s 资源事件创建专门的通知
        if ($event instanceof BaseK8sResourceEvent) {
            return K8sResourceWebhookNotification::fromK8sEvent($event);
        }

        // 为其他事件创建通用通知
        $eventData = $this->extractEventData($event);
        return new GenericWebhookNotification($eventType, $eventData);
    }

    /**
     * 从事件中提取数据
     */
    private function extractEventData(object $event): array
    {
        $data = [];

        // 获取事件的公共属性
        $reflection = new \ReflectionClass($event);
        foreach ($reflection->getProperties(\ReflectionProperty::IS_PUBLIC) as $property) {
            $name = $property->getName();
            if ($property->isInitialized($event)) {
                $data[$name] = $property->getValue($event);
            }
        }

        // 如果事件有 toArray 方法，使用它
        if (method_exists($event, 'toArray')) {
            $data = array_merge($data, $event->toArray());
        }

        return $data;
    }

    /**
     * Handle a job failure.
     */
    public function failed(object $event, \Throwable $exception): void
    {
        $eventType = $this->determineEventType($event);
        
        Log::error('Webhook notification listener failed', [
            'event_type' => $eventType,
            'event_class' => get_class($event),
            'error' => $exception->getMessage(),
        ]);
    }
}
