<?php

namespace App\Service;

use App\Models\WebhookDelivery;
use App\Models\WebhookEndpoint;
use Illuminate\Support\Facades\Log;
use Spatie\WebhookServer\WebhookCall;

/**
 * @deprecated This service will be replaced by the new Webhook Notification system
 * Use WebhookChannel and WebhookNotification instead
 */
class WebhookService
{
    /**
     * 发送webhook到指定端点
     */
    public function sendWebhook(WebhookEndpoint $endpoint, string $eventType, array $payload): WebhookDelivery
    {
        // 创建delivery记录
        $delivery = WebhookDelivery::create([
            'webhook_endpoint_id' => $endpoint->id,
            'url' => $endpoint->url,
            'event_type' => $eventType,
            'payload' => $payload,
            'headers' => $endpoint->headers ?? [],
            'status' => 'pending',
        ]);

        try {
            // 使用 spatie/laravel-webhook-server 发送 webhook
            $webhookCall = WebhookCall::create()
                ->url($endpoint->url)
                ->payload($payload)
                ->timeoutInSeconds($endpoint->timeout)
                ->maximumTries($endpoint->max_attempts)
                ->withHeaders($endpoint->headers ?? [])
                ->meta([
                    'delivery_id' => $delivery->id,
                    'endpoint_id' => $endpoint->id,
                    'event_type' => $eventType,
                ]);

            // 如果有secret，使用签名
            if ($endpoint->secret) {
                $webhookCall->useSecret($endpoint->secret);
            } else {
                $webhookCall->doNotSign();
            }

            // 异步发送
            $webhookCall->dispatch();

            Log::info('Webhook dispatched successfully', [
                'endpoint_id' => $endpoint->id,
                'delivery_id' => $delivery->id,
                'event_type' => $eventType,
            ]);

            // 更新endpoint的最后发送时间
            $endpoint->update(['last_delivered_at' => now()]);

        } catch (\Exception $e) {
            $delivery->markAsFailed($e->getMessage());

            Log::error('Webhook dispatch failed', [
                'endpoint_id' => $endpoint->id,
                'delivery_id' => $delivery->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $delivery;
    }

    /**
     * 重试失败的webhook发送
     * 注意：使用 spatie/laravel-webhook-server 时，重试机制由库自动处理
     * 这个方法主要用于手动重试或特殊情况
     */
    public function retryFailedDeliveries(): void
    {
        $failedDeliveries = WebhookDelivery::where('status', 'failed')
            ->where('next_retry_at', '<=', now())
            ->with('webhookEndpoint')
            ->get();

        foreach ($failedDeliveries as $delivery) {
            if ($delivery->shouldRetry() && $delivery->webhookEndpoint) {
                Log::info('Manually retrying webhook delivery', [
                    'delivery_id' => $delivery->id,
                    'attempt' => $delivery->attempts + 1,
                ]);

                // 重新发送 webhook
                $this->sendWebhook(
                    $delivery->webhookEndpoint,
                    $delivery->event_type,
                    $delivery->payload
                );
            }
        }
    }

    /**
     * 测试webhook端点
     */
    public function testEndpoint(WebhookEndpoint $endpoint): WebhookDelivery
    {
        $testPayload = [
            'event_type' => 'test',
            'message' => 'This is a test webhook from PaaS',
            'webhook_endpoint' => [
                'id' => $endpoint->id,
                'name' => $endpoint->name,
            ],
            'timestamp' => now()->toISOString(),
        ];

        return $this->sendWebhook($endpoint, 'test', $testPayload);
    }

    /**
     * 同步发送webhook（用于测试或特殊情况）
     */
    public function sendWebhookSync(WebhookEndpoint $endpoint, string $eventType, array $payload): WebhookDelivery
    {
        // 创建delivery记录
        $delivery = WebhookDelivery::create([
            'webhook_endpoint_id' => $endpoint->id,
            'url' => $endpoint->url,
            'event_type' => $eventType,
            'payload' => $payload,
            'headers' => $endpoint->headers ?? [],
            'status' => 'pending',
        ]);

        try {
            // 使用 spatie/laravel-webhook-server 同步发送
            $webhookCall = WebhookCall::create()
                ->url($endpoint->url)
                ->payload($payload)
                ->timeoutInSeconds($endpoint->timeout)
                ->withHeaders($endpoint->headers ?? [])
                ->meta([
                    'delivery_id' => $delivery->id,
                    'endpoint_id' => $endpoint->id,
                    'event_type' => $eventType,
                ]);

            // 如果有secret，使用签名
            if ($endpoint->secret) {
                $webhookCall->useSecret($endpoint->secret);
            } else {
                $webhookCall->doNotSign();
            }

            // 同步发送
            $webhookCall->dispatchSync();

            Log::info('Webhook sent synchronously', [
                'endpoint_id' => $endpoint->id,
                'delivery_id' => $delivery->id,
                'event_type' => $eventType,
            ]);

        } catch (\Exception $e) {
            $delivery->markAsFailed($e->getMessage());

            Log::error('Sync webhook failed', [
                'endpoint_id' => $endpoint->id,
                'delivery_id' => $delivery->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $delivery;
    }
}
