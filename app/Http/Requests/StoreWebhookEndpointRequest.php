<?php

namespace App\Http\Requests;

use App\Services\WebhookEventRegistry;
use Illuminate\Foundation\Http\FormRequest;

class StoreWebhookEndpointRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        // 从事件注册系统获取可用事件
        $eventRegistry = app(WebhookEventRegistry::class);
        $availableEvents = $eventRegistry->getValidationEventTypes();

        return [
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:2048',
            'events' => 'required|array|min:1',
            'events.*' => 'string|in:'.implode(',', $availableEvents),
            'headers' => 'nullable|array',
            'headers.*' => 'string|max:1000',
            'is_active' => 'boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => '名称不能为空',
            'name.max' => '名称长度不能超过255个字符',
            'url.required' => 'URL不能为空',
            'url.url' => 'URL格式不正确',
            'url.max' => 'URL长度不能超过2048个字符',
            'events.required' => '至少需要选择一个事件类型',
            'events.min' => '至少需要选择一个事件类型',
            'events.*.in' => '无效的事件类型',
            'headers.array' => '请求头必须是数组格式',
            'headers.*.max' => '请求头值长度不能超过1000个字符',
        ];
    }
}
