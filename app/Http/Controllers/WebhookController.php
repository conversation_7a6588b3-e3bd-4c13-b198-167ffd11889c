<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreWebhookEndpointRequest;
use App\Http\Requests\UpdateWebhookEndpointRequest;
use App\Http\Resources\WebhookDeliveryResource;
use App\Http\Resources\WebhookEndpointResource;
use App\Models\WebhookDelivery;
use App\Models\WebhookEndpoint;
use App\Notifications\GenericWebhookNotification;
use App\Service\WebhookService;
use App\Services\WebhookEventRegistry;
use Inertia\Inertia;

class WebhookController extends Controller
{
    public function __construct(
        private WebhookService $webhookService,
        private WebhookEventRegistry $eventRegistry
    ) {}

    /**
     * 显示 Webhook 列表页面
     */
    public function index()
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        $webhooks = $workspace->webhookEndpoints()
            ->withCount([
                'deliveries',
                'deliveries as successful_deliveries_count' => function ($query) {
                    $query->where('status', 'success');
                },
            ])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Webhooks/Index', [
            'workspace' => $workspace,
            'webhooks' => WebhookEndpointResource::collection($webhooks)->resolve(),
        ]);
    }

    /**
     * 显示创建 Webhook 页面
     */
    public function create()
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        return Inertia::render('Webhooks/Create', [
            'workspace' => $workspace,
            'availableEvents' => $this->eventRegistry->getEventsByGroup(),
        ]);
    }

    /**
     * 存储新的 Webhook
     */
    public function store(StoreWebhookEndpointRequest $request)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        $webhook = $workspace->webhookEndpoints()->create($request->validated());

        return redirect()
            ->route('webhooks.show', $webhook)
            ->with('success', 'Webhook 创建成功！');
    }

    /**
     * 显示 Webhook 详情页面
     */
    public function show(WebhookEndpoint $webhook)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 确保webhook属于当前workspace
        if ($webhook->workspace_id !== $workspace->id) {
            abort(404);
        }

        $deliveries = $webhook->deliveries()
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get();

        // 计算统计数据
        $totalDeliveries = $webhook->deliveries()->count();
        $successfulDeliveries = $webhook->deliveries()->where('status', 'success')->count();
        $failedDeliveries = $webhook->deliveries()->where('status', 'failed')->count();
        $pendingDeliveries = $webhook->deliveries()->where('status', 'pending')->count();

        $stats = [
            'total' => $totalDeliveries,
            'successful' => $successfulDeliveries,
            'failed' => $failedDeliveries,
            'pending' => $pendingDeliveries,
            'success_rate' => $totalDeliveries > 0 ? round(($successfulDeliveries / $totalDeliveries) * 100, 1) : 0,
        ];

        return Inertia::render('Webhooks/Show', [
            'workspace' => $workspace,
            'webhook' => (new WebhookEndpointResource($webhook))->resolve(),
            'deliveries' => WebhookDeliveryResource::collection($deliveries)->resolve(),
            'stats' => $stats,
        ]);
    }

    /**
     * 获取可用的 Webhook 事件类型 (API)
     */
    public function events()
    {
        return response()->json([
            'events' => $this->eventRegistry->getEventsByGroup(),
            'validation_types' => $this->eventRegistry->getValidationEventTypes(),
        ]);
    }

    /**
     * 显示编辑 Webhook 页面
     */
    public function edit(WebhookEndpoint $webhook)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 确保webhook属于当前workspace
        if ($webhook->workspace_id !== $workspace->id) {
            abort(404);
        }

        return Inertia::render('Webhooks/Edit', [
            'workspace' => $workspace,
            'webhook' => (new WebhookEndpointResource($webhook))->resolve(),
            'availableEvents' => $this->eventRegistry->getEventsByGroup(),
        ]);
    }

    /**
     * 更新 Webhook
     */
    public function update(UpdateWebhookEndpointRequest $request, WebhookEndpoint $webhook)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 确保webhook属于当前workspace
        if ($webhook->workspace_id !== $workspace->id) {
            abort(404);
        }

        $webhook->update($request->validated());

        return redirect()
            ->route('webhooks.show', $webhook)
            ->with('success', 'Webhook 更新成功！');
    }

    /**
     * 删除 Webhook
     */
    public function destroy(WebhookEndpoint $webhook)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 确保webhook属于当前workspace
        if ($webhook->workspace_id !== $workspace->id) {
            abort(404);
        }

        $webhook->delete();

        return redirect()
            ->route('webhooks.index')
            ->with('success', 'Webhook 删除成功！');
    }

    /**
     * 切换 Webhook 启用状态
     */
    public function toggle(WebhookEndpoint $webhook)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 确保webhook属于当前workspace
        if ($webhook->workspace_id !== $workspace->id) {
            abort(404);
        }

        $webhook->update(['is_active' => ! $webhook->is_active]);

        return back()->with('success', $webhook->is_active ? 'Webhook 已启用' : 'Webhook 已禁用');
    }

    /**
     * 重新生成 Secret
     */
    public function regenerateSecret(WebhookEndpoint $webhook)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 确保webhook属于当前workspace
        if ($webhook->workspace_id !== $workspace->id) {
            abort(404);
        }

        $webhook->regenerateSecret();

        return back()->with('success', 'Secret 重新生成成功！');
    }

    /**
     * 测试 Webhook
     */
    public function test(WebhookEndpoint $webhook)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 确保webhook属于当前workspace
        if ($webhook->workspace_id !== $workspace->id) {
            abort(404);
        }

        try {
            // 使用新的通知系统发送测试 Webhook
            $testData = [
                'message' => 'This is a test webhook from PaaS',
                'webhook_endpoint' => [
                    'id' => $webhook->id,
                    'name' => $webhook->name,
                ],
                'workspace' => [
                    'id' => $workspace->id,
                    'name' => $workspace->name,
                    'namespace' => $workspace->namespace,
                ],
                'test_timestamp' => now()->toISOString(),
            ];

            $notification = new GenericWebhookNotification('test', $testData);
            $workspace->notify($notification);

            return back()->with('success', '测试 Webhook 已发送！');
        } catch (\Exception $e) {
            return back()->with('error', '测试 Webhook 发送失败：'.$e->getMessage());
        }
    }

    /**
     * 显示 Webhook Delivery 详情
     */
    public function showDelivery(WebhookEndpoint $webhook, WebhookDelivery $delivery)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 确保webhook和delivery都属于当前workspace
        if ($webhook->workspace_id !== $workspace->id || $delivery->webhook_endpoint_id !== $webhook->id) {
            abort(404);
        }

        return Inertia::render('Webhooks/DeliveryShow', [
            'workspace' => $workspace,
            'webhook' => new WebhookEndpointResource($webhook),
            'delivery' => new WebhookDeliveryResource($delivery),
        ]);
    }
}
