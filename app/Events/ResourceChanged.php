<?php

namespace App\Events;

use App\Events\K8s\ConfigMapCreated;
use App\Events\K8s\ConfigMapDeleted;
use App\Events\K8s\ConfigMapUpdated;
use App\Events\K8s\DeploymentCreated;
use App\Events\K8s\DeploymentDeleted;
use App\Events\K8s\DeploymentUpdated;
use App\Events\K8s\EventCreated;
use App\Events\K8s\EventDeleted;
use App\Events\K8s\EventUpdated;
use App\Events\K8s\HorizontalPodAutoscalerCreated;
use App\Events\K8s\HorizontalPodAutoscalerDeleted;
use App\Events\K8s\HorizontalPodAutoscalerUpdated;
use App\Events\K8s\IngressCreated;
use App\Events\K8s\IngressDeleted;
use App\Events\K8s\IngressUpdated;
use App\Events\K8s\PersistentVolumeClaimCreated;
use App\Events\K8s\PersistentVolumeClaimDeleted;
use App\Events\K8s\PersistentVolumeClaimUpdated;
use App\Events\K8s\PodCreated;
use App\Events\K8s\PodDeleted;
use App\Events\K8s\PodUpdated;
use App\Events\K8s\SecretCreated;
use App\Events\K8s\SecretDeleted;
use App\Events\K8s\SecretUpdated;
use App\Events\K8s\ServiceCreated;
use App\Events\K8s\ServiceDeleted;
use App\Events\K8s\ServiceUpdated;
use App\Events\K8s\StatefulSetCreated;
use App\Events\K8s\StatefulSetDeleted;
use App\Events\K8s\StatefulSetUpdated;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ResourceChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $namespace;

    public string $clusterName;

    public int $clusterId;

    public string $resourceType;

    public array $changes;

    public array $summary;

    /**
     * Resource type to event class mapping
     */
    private static array $eventMapping = [
        'deployments' => [
            'created' => DeploymentCreated::class,
            'updated' => DeploymentUpdated::class,
            'deleted' => DeploymentDeleted::class,
        ],
        'statefulsets' => [
            'created' => StatefulSetCreated::class,
            'updated' => StatefulSetUpdated::class,
            'deleted' => StatefulSetDeleted::class,
        ],
        'services' => [
            'created' => ServiceCreated::class,
            'updated' => ServiceUpdated::class,
            'deleted' => ServiceDeleted::class,
        ],
        'ingresses' => [
            'created' => IngressCreated::class,
            'updated' => IngressUpdated::class,
            'deleted' => IngressDeleted::class,
        ],
        'pods' => [
            'created' => PodCreated::class,
            'updated' => PodUpdated::class,
            'deleted' => PodDeleted::class,
        ],
        'secrets' => [
            'created' => SecretCreated::class,
            'updated' => SecretUpdated::class,
            'deleted' => SecretDeleted::class,
        ],
        'configmaps' => [
            'created' => ConfigMapCreated::class,
            'updated' => ConfigMapUpdated::class,
            'deleted' => ConfigMapDeleted::class,
        ],
        'persistentvolumeclaims' => [
            'created' => PersistentVolumeClaimCreated::class,
            'updated' => PersistentVolumeClaimUpdated::class,
            'deleted' => PersistentVolumeClaimDeleted::class,
        ],
        'storages' => [
            'created' => PersistentVolumeClaimCreated::class,
            'updated' => PersistentVolumeClaimUpdated::class,
            'deleted' => PersistentVolumeClaimDeleted::class,
        ],
        'horizontalpodautoscalers' => [
            'created' => HorizontalPodAutoscalerCreated::class,
            'updated' => HorizontalPodAutoscalerUpdated::class,
            'deleted' => HorizontalPodAutoscalerDeleted::class,
        ],
        'events' => [
            'created' => EventCreated::class,
            'updated' => EventUpdated::class,
            'deleted' => EventDeleted::class,
        ],
    ];

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceType,
        array $changes
    ) {
        $this->namespace = $namespace;
        $this->clusterName = $clusterName;
        $this->clusterId = $clusterId;
        $this->resourceType = $resourceType;
        $this->changes = $changes;
        $this->summary = $this->generateSummary($changes);

        // Trigger specific K8s events for each resource change
        $this->dispatchSpecificK8sEvents();
    }

    /**
     * Generate a summary of the changes.
     */
    private function generateSummary(array $changes): array
    {
        return [
            'created_count' => count($changes['created'] ?? []),
            'updated_count' => count($changes['updated'] ?? []),
            'deleted_count' => count($changes['deleted'] ?? []),
            'total_changes' => count($changes['created'] ?? []) +
                count($changes['updated'] ?? []) +
                count($changes['deleted'] ?? []),
        ];
    }

    /**
     * Dispatch specific K8s events for each resource change
     */
    private function dispatchSpecificK8sEvents(): void
    {
        // Handle created resources
        if (! empty($this->changes['created'])) {
            foreach ($this->changes['created'] as $resource) {
                $this->createSpecificEvent('created', $resource);
            }
        }

        // Handle updated resources
        if (! empty($this->changes['updated'])) {
            foreach ($this->changes['updated'] as $resource) {
                // Extract previous resource if available
                $previousResource = $resource['_previous'] ?? [];
                unset($resource['_previous']); // Remove from current resource

                $this->createSpecificEvent('updated', $resource, $previousResource);
            }
        }

        // Handle deleted resources
        if (! empty($this->changes['deleted'])) {
            foreach ($this->changes['deleted'] as $resource) {
                $this->createSpecificEvent('deleted', $resource);
            }
        }
    }

    /**
     * Create and dispatch a specific K8s event
     */
    private function createSpecificEvent(string $action, array $resource, array $previousResource = []): void
    {
        // Get the resource name robustly
        $resourceName = null;
        if (isset($resource['metadata']['name'])) {
            $resourceName = $resource['metadata']['name'];
        } elseif (isset($resource['name'])) {
            $resourceName = $resource['name'];
        } elseif (isset($resource['metadata']) && is_array($resource['metadata'])) {
            // Try to find a name in metadata if possible
            foreach (['generateName', 'selfLink', 'uid'] as $altKey) {
                if (isset($resource['metadata'][$altKey])) {
                    $resourceName = $resource['metadata'][$altKey];
                    break;
                }
            }
        }

        if ($resourceName === null) {
            Log::error('Resource name not found', [
                'resource' => $resource,
                'action' => $action,
                'resource_type' => $this->resourceType,
            ]);

            // Optionally, continue instead of return, or throw exception if critical
            return;
        }

        Log::debug(sprintf('Creating specific event for %s %s.%s', $this->resourceType, $resourceName, $action));

        // Check if we have a mapping for this resource type and action
        if (! isset(self::$eventMapping[$this->resourceType][$action])) {
            Log::debug(sprintf('No event mapping found for resource type %s and action %s', $resourceName, $action));

            return; // Skip if no mapping found
        }

        $eventClass = self::$eventMapping[$this->resourceType][$action];

        Log::debug(sprintf('Event class: %s', $eventClass, $action));

        // Ensure the event class exists
        if (! class_exists($eventClass)) {
            Log::warning('K8s event class not found', [
                'resource_type' => $this->resourceType,
                'action' => $action,
                'event_class' => $eventClass,
            ]);

            return;
        }

        try {
            // Create and dispatch the specific event
            if ($action === 'updated' && ! empty($previousResource)) {
                // Updated events need previous resource
                $event = new $eventClass(
                    $this->namespace,
                    $this->clusterName,
                    $this->clusterId,
                    $resourceName,
                    $resource,
                    $previousResource
                );
            } else {
                // Created and deleted events
                $event = new $eventClass(
                    $this->namespace,
                    $this->clusterName,
                    $this->clusterId,
                    $resourceName,
                    $resource
                );
            }

            event($event);
        } catch (\Throwable $e) {
            // Log the error but don't break the main event
            \Log::warning('Failed to dispatch specific K8s event', [
                'resource_type' => $this->resourceType,
                'action' => $action,
                'resource_name' => $resourceName,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("workspace.{$this->namespace}.resources"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'namespace' => $this->namespace,
            'cluster' => [
                'id' => $this->clusterId,
                'name' => $this->clusterName,
            ],
            'resource_type' => $this->resourceType,
            'changes' => $this->changes,
            'summary' => $this->summary,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return 'resource.changed';
    }
}
