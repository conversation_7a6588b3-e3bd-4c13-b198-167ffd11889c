<?php

namespace App\Console\Commands;

use App\Models\Workspace;
use App\Models\WebhookEndpoint;
use App\Notifications\GenericWebhookNotification;
use App\Services\WebhookEventRegistry;
use Illuminate\Console\Command;

class TestWebhookSystem extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'webhook:test 
                            {--workspace= : Workspace ID to test with}
                            {--event=test : Event type to send}
                            {--list-events : List all registered events}';

    /**
     * The console command description.
     */
    protected $description = 'Test the new webhook notification system';

    /**
     * Execute the console command.
     */
    public function handle(WebhookEventRegistry $eventRegistry): int
    {
        if ($this->option('list-events')) {
            $this->listEvents($eventRegistry);
            return 0;
        }

        $workspaceId = $this->option('workspace');
        $eventType = $this->option('event');

        if (!$workspaceId) {
            $this->error('Please specify a workspace ID with --workspace option');
            return 1;
        }

        $workspace = Workspace::find($workspaceId);
        if (!$workspace) {
            $this->error("Workspace with ID {$workspaceId} not found");
            return 1;
        }

        // 检查事件是否已注册
        if (!$eventRegistry->isRegistered($eventType) && $eventType !== 'test') {
            $this->warn("Event type '{$eventType}' is not registered in the event registry");
            $this->info("Use --list-events to see all registered events");
        }

        // 查找该工作区的 webhook 端点
        $endpoints = $workspace->webhookEndpoints()
            ->where('is_active', true)
            ->get();

        if ($endpoints->isEmpty()) {
            $this->warn("No active webhook endpoints found for workspace '{$workspace->name}'");
            return 1;
        }

        $this->info("Found {$endpoints->count()} active webhook endpoint(s) for workspace '{$workspace->name}':");
        foreach ($endpoints as $endpoint) {
            $this->line("  - {$endpoint->name} ({$endpoint->url})");
            $this->line("    Events: " . implode(', ', $endpoint->events));
        }

        // 发送测试通知
        $testData = [
            'message' => 'This is a test webhook notification from the new system',
            'workspace' => [
                'id' => $workspace->id,
                'name' => $workspace->name,
                'namespace' => $workspace->namespace,
            ],
            'test_timestamp' => now()->toISOString(),
            'system' => 'PaaS Webhook System v2.0',
        ];

        $this->info("\nSending test notification...");
        
        try {
            $notification = new GenericWebhookNotification($eventType, $testData);
            $workspace->notify($notification);
            
            $this->info("✅ Test notification sent successfully!");
            $this->info("Event type: {$eventType}");
            $this->info("Check your webhook endpoints to verify delivery.");
            
        } catch (\Exception $e) {
            $this->error("❌ Failed to send test notification: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * List all registered events
     */
    private function listEvents(WebhookEventRegistry $eventRegistry): void
    {
        $this->info('Registered Webhook Events:');
        $this->newLine();

        $eventsByGroup = $eventRegistry->getEventsByGroup();

        foreach ($eventsByGroup as $group => $events) {
            $this->info("📁 {$group}:");
            foreach ($events as $event) {
                $description = $event['description'] ?: 'No description';
                $this->line("  • {$event['type']} - {$description}");
            }
            $this->newLine();
        }

        $totalEvents = count($eventRegistry->getAllEventTypes());
        $this->info("Total registered events: {$totalEvents}");
        $this->info("Plus wildcard (*) for all events");
    }
}
