<?php

namespace App\Console\Commands;

use App\Listeners\WebhookEventListener;
use App\Listeners\GenericWebhookListener;
use App\Services\WebhookEventRegistry;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class MigrateWebhookSystem extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'webhook:migrate 
                            {--dry-run : Show what would be done without making changes}
                            {--remove-old : Remove old webhook files after migration}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate from old webhook system to new notification-based system';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $removeOld = $this->option('remove-old');

        $this->info('🔄 Migrating Webhook System');
        $this->newLine();

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
            $this->newLine();
        }

        // 1. 检查旧文件
        $this->checkOldFiles($dryRun, $removeOld);

        // 2. 验证新系统
        $this->verifyNewSystem();

        // 3. 检查数据库兼容性
        $this->checkDatabaseCompatibility();

        $this->newLine();
        $this->info('✅ Migration analysis complete!');
        
        if (!$dryRun) {
            $this->info('🎉 New webhook system is ready to use!');
            $this->newLine();
            $this->info('Next steps:');
            $this->line('1. Test the new system: php artisan webhook:test --workspace=<id>');
            $this->line('2. List available events: php artisan webhook:test --list-events');
            $this->line('3. Update your application to use the new notification system');
        }

        return 0;
    }

    /**
     * Check old webhook files
     */
    private function checkOldFiles(bool $dryRun, bool $removeOld): void
    {
        $this->info('📁 Checking old webhook files...');

        $oldFiles = [
            'app/Listeners/WebhookEventListener.php',
            'app/Listeners/GenericWebhookListener.php',
        ];

        $filesToRemove = [];

        foreach ($oldFiles as $file) {
            if (File::exists(base_path($file))) {
                $this->line("  ⚠️  Found old file: {$file}");
                
                if ($removeOld) {
                    $filesToRemove[] = $file;
                }
            } else {
                $this->line("  ✅ Old file not found: {$file}");
            }
        }

        if (!empty($filesToRemove)) {
            if ($dryRun) {
                $this->warn("  Would remove " . count($filesToRemove) . " old files");
            } else {
                foreach ($filesToRemove as $file) {
                    File::delete(base_path($file));
                    $this->line("  🗑️  Removed: {$file}");
                }
            }
        }

        $this->newLine();
    }

    /**
     * Verify new system components
     */
    private function verifyNewSystem(): void
    {
        $this->info('🔍 Verifying new webhook system...');

        $newFiles = [
            'app/Services/WebhookEventRegistry.php',
            'app/Providers/WebhookServiceProvider.php',
            'app/Notifications/Channels/WebhookChannel.php',
            'app/Notifications/WebhookNotification.php',
            'app/Notifications/K8sResourceWebhookNotification.php',
            'app/Notifications/GenericWebhookNotification.php',
            'app/Listeners/WebhookNotificationListener.php',
            'app/Traits/HasWebhookNotifications.php',
            'config/webhooks.php',
        ];

        foreach ($newFiles as $file) {
            if (File::exists(base_path($file))) {
                $this->line("  ✅ {$file}");
            } else {
                $this->line("  ❌ Missing: {$file}");
            }
        }

        // 检查服务提供者是否已注册
        $providersFile = base_path('bootstrap/providers.php');
        if (File::exists($providersFile)) {
            $content = File::get($providersFile);
            if (str_contains($content, 'WebhookServiceProvider')) {
                $this->line("  ✅ WebhookServiceProvider registered");
            } else {
                $this->line("  ❌ WebhookServiceProvider not registered in bootstrap/providers.php");
            }
        }

        // 检查事件注册系统
        try {
            $registry = app(WebhookEventRegistry::class);
            $eventCount = count($registry->getAllEventTypes());
            $this->line("  ✅ Event registry working ({$eventCount} events registered)");
        } catch (\Exception $e) {
            $this->line("  ❌ Event registry error: " . $e->getMessage());
        }

        $this->newLine();
    }

    /**
     * Check database compatibility
     */
    private function checkDatabaseCompatibility(): void
    {
        $this->info('🗄️  Checking database compatibility...');

        try {
            // 检查 webhook_endpoints 表
            $endpoints = \App\Models\WebhookEndpoint::count();
            $this->line("  ✅ webhook_endpoints table accessible ({$endpoints} endpoints)");

            // 检查 webhook_deliveries 表
            $deliveries = \App\Models\WebhookDelivery::count();
            $this->line("  ✅ webhook_deliveries table accessible ({$deliveries} deliveries)");

            // 检查 workspaces 表
            $workspaces = \App\Models\Workspace::count();
            $this->line("  ✅ workspaces table accessible ({$workspaces} workspaces)");

            // 检查 Workspace 模型是否有 Notifiable trait
            $workspace = new \App\Models\Workspace();
            if (method_exists($workspace, 'notify')) {
                $this->line("  ✅ Workspace model has Notifiable trait");
            } else {
                $this->line("  ❌ Workspace model missing Notifiable trait");
            }

        } catch (\Exception $e) {
            $this->line("  ❌ Database error: " . $e->getMessage());
        }

        $this->newLine();
    }
}
