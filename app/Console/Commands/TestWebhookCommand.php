<?php

namespace App\Console\Commands;

use App\Models\WebhookEndpoint;
use App\Service\WebhookService;
use Illuminate\Console\Command;

/**
 * 测试 Webhook 发送的命令
 */
class TestWebhookCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'webhook:test {endpoint_id : The webhook endpoint ID to test}';

    /**
     * The console command description.
     */
    protected $description = 'Test a webhook endpoint by sending a test payload';

    /**
     * Execute the console command.
     */
    public function handle(WebhookService $webhookService): int
    {
        $endpointId = $this->argument('endpoint_id');
        
        $endpoint = WebhookEndpoint::find($endpointId);
        
        if (!$endpoint) {
            $this->error("Webhook endpoint with ID {$endpointId} not found.");
            return 1;
        }

        $this->info("Testing webhook endpoint: {$endpoint->name}");
        $this->info("URL: {$endpoint->url}");
        
        try {
            $delivery = $webhookService->testEndpoint($endpoint);
            
            $this->info("Test webhook dispatched successfully!");
            $this->info("Delivery ID: {$delivery->id}");
            $this->info("Status: {$delivery->status}");
            
            $this->line('');
            $this->info('You can check the delivery status in the webhook_deliveries table or through the admin interface.');
            
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to send test webhook: {$e->getMessage()}");
            return 1;
        }
    }
}
